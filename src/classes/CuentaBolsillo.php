<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class CuentaBolsillo
{
    // --- Atributos ---
    private ?int    $id     = null;
    private ?string $nombre = null;
    private ?float  $valor  = null;
    private ?int    $estado = null;

    /**
     * Constructor: Inicializa las propiedades del objeto CuentaBolsillo.
     */
    public function __construct()
    {
        $this->id     = 0;
        $this->nombre = null;
        $this->valor  = 0.0;
        $this->estado = 1; // Estado activo por defecto
    }

    /**
     * Método estático para construir un objeto CuentaBolsillo desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos de la cuenta bolsillo.
     *
     * @return self Instancia de CuentaBolsillo.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto         = new self();
            $objeto->id     = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->nombre = $resultado['nombre'] ?? null;
            $objeto->valor  = isset($resultado['valor']) ? (float)$resultado['valor'] : 0.0;
            $objeto->estado = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir CuentaBolsillo: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene una cuenta bolsillo por su ID.
     *
     * @param int $id       ID de la cuenta bolsillo.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto CuentaBolsillo o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM cuentas_bolsillos
            WHERE
                id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener CuentaBolsillo (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de cuentas bolsillos activas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos CuentaBolsillo.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                *
            FROM cuentas_bolsillos
            WHERE
                estado = 1
            ORDER BY
                nombre
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de CuentasBolsillos: " . $e->getMessage());
        }
    }

    /**
     * Verifica si un nombre ya existe en la base de datos.
     *
     * @param string $nombre Nombre a verificar.
     * @param PDO $conexion Conexión PDO.
     * @param int|null $excludeId ID de la cuenta a excluir de la búsqueda (para modificaciones).
     *
     * @return bool True si el nombre ya existe, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private static function nombreExiste(string $nombre, PDO $conexion, ?int $excludeId = null): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM cuentas_bolsillos
            WHERE nombre = :nombre
            SQL;

            if ($excludeId !== null) {
                $query .= " AND id != :excludeId";
            }

            $statement = $conexion->prepare($query);
            $statement->bindValue(':nombre', $nombre, PDO::PARAM_STR);

            if ($excludeId !== null) {
                $statement->bindValue(':excludeId', $excludeId, PDO::PARAM_INT);
            }

            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return (int)$resultado['count'] > 0;

        } catch (PDOException $e) {
            throw new Exception("Error al verificar nombre duplicado: " . $e->getMessage());
        }
    }

    /**
     * Crea una nueva cuenta bolsillo en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID de la nueva cuenta creada o false en caso de error.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas
        if (empty($this->getNombre())) {
            throw new Exception("El nombre es requerido para crear la cuenta bolsillo.");
        }

        if ($this->getValor() === null || $this->getValor() < 0) {
            throw new Exception("El valor debe ser un número válido mayor o igual a 0.");
        }

        // Validar que el nombre no esté duplicado
        if (self::nombreExiste($this->getNombre(), $conexion)) {
            throw new Exception("Error al crear cuenta bolsillo: El nombre '{$this->getNombre()}' ya existe.");
        }

        try {
            $query = <<<SQL
            INSERT INTO cuentas_bolsillos (
                 nombre
                ,valor
                ,estado
            ) VALUES (
                 :nombre
                ,:valor
                ,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
            $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            if ($e->getCode() == 23000 || $e->getCode() == 1062) {
                throw new Exception("Error al crear cuenta bolsillo: El nombre '{$this->getNombre()}' ya existe.");
            } else {
                throw new Exception("Error de base de datos al crear cuenta bolsillo: " . $e->getMessage());
            }
        } catch (Exception $e) {
            throw new Exception("Error al crear cuenta bolsillo: " . $e->getMessage());
        }
    }

    /**
     * Modifica los datos de una cuenta bolsillo existente.
     *
     * @param int    $id         ID de la cuenta a modificar.
     * @param string $nuevoNombre El nuevo nombre para la cuenta.
     * @param float  $nuevoValor  El nuevo valor para la cuenta.
     * @param PDO    $conexion   Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos están vacíos o si ocurre un error de base de datos.
     */
    public static function modificar(int $id, string $nuevoNombre, float $nuevoValor, PDO $conexion): bool
    {
        if (empty(trim($nuevoNombre))) {
            throw new Exception("El nombre no puede estar vacío.");
        }

        if ($nuevoValor < 0) {
            throw new Exception("El valor debe ser mayor o igual a 0.");
        }

        // Validar que el nombre no esté duplicado (excluyendo la cuenta actual)
        if (self::nombreExiste($nuevoNombre, $conexion, $id)) {
            throw new Exception("Error al modificar cuenta bolsillo: El nombre '$nuevoNombre' ya existe.");
        }

        try {
            $query = <<<SQL
            UPDATE cuentas_bolsillos SET
                nombre = :nombre,
                valor = :valor
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':nombre', trim($nuevoNombre), PDO::PARAM_STR);
            $statement->bindValue(':valor', $nuevoValor, PDO::PARAM_STR);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar cuenta bolsillo (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Desactiva una cuenta bolsillo estableciendo su estado a 0.
     *
     * @param int $id       ID de la cuenta a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE cuentas_bolsillos SET
                estado = 0
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar cuenta bolsillo (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si la cuenta bolsillo está activa.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }
}
