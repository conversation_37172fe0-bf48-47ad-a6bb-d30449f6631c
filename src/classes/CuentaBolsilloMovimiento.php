<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class CuentaBolsilloMovimiento
{
    // --- Atributos ---
    private ?int    $id          = null;
    private ?int    $id_bolsillo = null;
    private ?string $tipo        = null;
    private ?float  $valor       = null;
    private ?string $fecha       = null;
    private ?string $nota        = null;
    private ?int    $estado      = null;
    
    // Atributo adicional para evitar lookups redundantes en vistas
    private ?string $nombre_bolsillo = null;

    /**
     * Constructor: Inicializa las propiedades del objeto CuentaBolsilloMovimiento.
     */
    public function __construct()
    {
        $this->id          = 0;
        $this->id_bolsillo = null;
        $this->tipo        = null;
        $this->valor       = 0.0;
        $this->fecha       = null;
        $this->nota        = null;
        $this->estado      = 1; // Estado activo por defecto
        $this->nombre_bolsillo = null;
    }

    /**
     * Método estático para construir un objeto CuentaBolsilloMovimiento desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del movimiento.
     *
     * @return self Instancia de CuentaBolsilloMovimiento.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                  = new self();
            $objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : 0;
            $objeto->id_bolsillo     = isset($resultado['id_bolsillo']) ? (int)$resultado['id_bolsillo'] : null;
            $objeto->tipo            = $resultado['tipo'] ?? null;
            $objeto->valor           = isset($resultado['valor']) ? (float)$resultado['valor'] : 0.0;
            $objeto->fecha           = $resultado['fecha'] ?? null;
            $objeto->nota            = $resultado['nota'] ?? null;
            $objeto->estado          = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
            $objeto->nombre_bolsillo = $resultado['nombre_bolsillo'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir CuentaBolsilloMovimiento: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene un movimiento por su ID.
     *
     * @param int $id       ID del movimiento.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto CuentaBolsilloMovimiento o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                cbm.*,
                cb.nombre as nombre_bolsillo
            FROM cuentas_bolsillos_movimientos cbm
            LEFT JOIN cuentas_bolsillos cb ON cbm.id_bolsillo = cb.id
            WHERE
                cbm.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener CuentaBolsilloMovimiento (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de movimientos activos.
     *
     * @param PDO $conexion Conexión PDO.
     * @param int|null $id_bolsillo Filtrar por bolsillo específico (opcional).
     *
     * @return array Array de objetos CuentaBolsilloMovimiento.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion, ?int $id_bolsillo = null): array
    {
        try {
            $query = <<<SQL
            SELECT
                cbm.*,
                cb.nombre as nombre_bolsillo
            FROM cuentas_bolsillos_movimientos cbm
            LEFT JOIN cuentas_bolsillos cb ON cbm.id_bolsillo = cb.id
            WHERE
                cbm.estado = 1
            SQL;

            if ($id_bolsillo !== null) {
                $query .= " AND cbm.id_bolsillo = :id_bolsillo";
            }

            $query .= " ORDER BY cbm.fecha DESC, cbm.id DESC";

            $statement = $conexion->prepare($query);
            
            if ($id_bolsillo !== null) {
                $statement->bindValue(':id_bolsillo', $id_bolsillo, PDO::PARAM_INT);
            }
            
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de CuentaBolsilloMovimientos: " . $e->getMessage());
        }
    }

    /**
     * Valida los datos del movimiento antes de guardar.
     *
     * @throws Exception Si los datos son inválidos.
     */
    private function validar_data(): void
    {
        // Configurar timezone para operaciones de fecha
        date_default_timezone_set('America/Bogota');

        if ($this->id_bolsillo === null || $this->id_bolsillo <= 0) {
            throw new Exception("Debe seleccionar un bolsillo válido.");
        }

        if (empty($this->tipo) || !in_array($this->tipo, ['ingreso', 'egreso'], true)) {
            throw new Exception("El tipo de movimiento debe ser 'ingreso' o 'egreso'.");
        }

        if ($this->valor === null || $this->valor <= 0) {
            throw new Exception("El valor debe ser un número mayor a 0.");
        }

        if (empty($this->fecha)) {
            throw new Exception("La fecha es obligatoria.");
        }

        // Validar formato de fecha yyyy-MM-dd
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->fecha)) {
            throw new Exception("La fecha debe tener el formato yyyy-MM-dd.");
        }

        // Validar que la fecha sea válida
        $fechaParts = explode('-', $this->fecha);
        if (!checkdate((int)$fechaParts[1], (int)$fechaParts[2], (int)$fechaParts[0])) {
            throw new Exception("La fecha proporcionada no es válida.");
        }

        if (!in_array($this->estado, [0, 1], true)) {
            throw new Exception("El estado debe ser 0 (Inactivo) o 1 (Activo).");
        }
    }

    /**
     * Crea un nuevo movimiento en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo movimiento creado o false en caso de error.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    public function crear(PDO $conexion): int|false
    {
        try {
            $this->validar_data();

            $query = <<<SQL
            INSERT INTO cuentas_bolsillos_movimientos (
                 id_bolsillo
                ,tipo
                ,valor
                ,fecha
                ,nota
                ,estado
            ) VALUES (
                 :id_bolsillo
                ,:tipo
                ,:valor
                ,:fecha
                ,:nota
                ,:estado
            )
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_bolsillo', $this->id_bolsillo, PDO::PARAM_INT);
            $statement->bindValue(':tipo', $this->tipo, PDO::PARAM_STR);
            $statement->bindValue(':valor', $this->valor, PDO::PARAM_STR);
            $statement->bindValue(':fecha', $this->fecha, PDO::PARAM_STR);
            $statement->bindValue(':nota', $this->nota, PDO::PARAM_STR);
            $statement->bindValue(':estado', $this->estado ?? 1, PDO::PARAM_INT);

            $success = $statement->execute();

            if ($success) {
                return (int)$conexion->lastInsertId();
            } else {
                return false;
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear movimiento: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear movimiento: " . $e->getMessage());
        }
    }

    /**
     * Modifica los datos de un movimiento existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos están vacíos o si ocurre un error de base de datos.
     */
    public function modificar(PDO $conexion): bool
    {
        if ($this->id === null || $this->id <= 0) {
            throw new Exception("ID del movimiento es requerido para modificar.");
        }

        try {
            $this->validar_data();

            $query = <<<SQL
            UPDATE cuentas_bolsillos_movimientos SET
                id_bolsillo = :id_bolsillo,
                tipo = :tipo,
                valor = :valor,
                fecha = :fecha,
                nota = :nota
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_bolsillo', $this->id_bolsillo, PDO::PARAM_INT);
            $statement->bindValue(':tipo', $this->tipo, PDO::PARAM_STR);
            $statement->bindValue(':valor', $this->valor, PDO::PARAM_STR);
            $statement->bindValue(':fecha', $this->fecha, PDO::PARAM_STR);
            $statement->bindValue(':nota', $this->nota, PDO::PARAM_STR);
            $statement->bindValue(':id', $this->id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar movimiento (ID: {$this->id}): " . $e->getMessage());
        }
    }

    /**
     * Métodos privados para inserción y actualización (siguiendo patrón del proyecto).
     */
    private function _insert(PDO $conexion): int|false
    {
        return $this->crear($conexion);
    }

    private function _update(PDO $conexion): bool
    {
        return $this->modificar($conexion);
    }

    /**
     * Desactiva un movimiento estableciendo su estado a 0.
     *
     * @param int $id       ID del movimiento a desactivar.
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la desactivación fue exitosa, False en caso contrario.
     * @throws Exception Si ocurre un error de base de datos.
     */
    public static function desactivar(int $id, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            UPDATE cuentas_bolsillos_movimientos SET
                estado = 0
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $id, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al desactivar movimiento (ID: $id): " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdBolsillo(): ?int
    {
        return $this->id_bolsillo;
    }

    public function setIdBolsillo(?int $id_bolsillo): self
    {
        $this->id_bolsillo = $id_bolsillo;
        return $this;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    public function setTipo(?string $tipo): self
    {
        $this->tipo = $tipo;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getFecha(): ?string
    {
        return $this->fecha;
    }

    public function setFecha(?string $fecha): self
    {
        $this->fecha = $fecha;
        return $this;
    }

    public function getNota(): ?string
    {
        return $this->nota;
    }

    public function setNota(?string $nota): self
    {
        $this->nota = $nota;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    public function getNombreBolsillo(): ?string
    {
        return $this->nombre_bolsillo;
    }

    public function setNombreBolsillo(?string $nombre_bolsillo): self
    {
        $this->nombre_bolsillo = $nombre_bolsillo;
        return $this;
    }

    // --- Métodos adicionales ---

    /**
     * Verifica si el movimiento está activo.
     * @return bool
     */
    public function isActivo(): bool
    {
        return $this->estado === 1;
    }

    /**
     * Verifica si el movimiento es un ingreso.
     * @return bool
     */
    public function isIngreso(): bool
    {
        return $this->tipo === 'ingreso';
    }

    /**
     * Verifica si el movimiento es un egreso.
     * @return bool
     */
    public function isEgreso(): bool
    {
        return $this->tipo === 'egreso';
    }

    /**
     * Formatea el valor como moneda colombiana.
     * @return string
     */
    public function getValorFormateado(): string
    {
        if ($this->valor === null) {
            return '$0 COP';
        }
        return '$' . number_format($this->valor, 0, ',', '.') . ' COP';
    }

    /**
     * Formatea la fecha en formato legible.
     * @return string
     */
    public function getFechaFormateada(): string
    {
        if (empty($this->fecha)) {
            return '';
        }

        try {
            $timestamp = strtotime($this->fecha);
            return date('d/m/Y', $timestamp);
        } catch (Exception $e) {
            return $this->fecha;
        }
    }
}
