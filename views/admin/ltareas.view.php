<?php
#region region DOCS

/** @var Tarea[] $tareas */
/** @var array $tareas_organizadas */
/** @var array $tareas_agentes_counts */
/** @var int|null $filtro_estado */
/** @var int|null $filtro_proyecto_id */
/** @var string|null $nombre_proyecto_filtro */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */

/** @var string $error_display */

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\ProyectoModulo;
use App\classes\TareaAgente;

#endregion DOCS

/**
 * Helper function to render note icon when a task has notes
 *
 * @param string|null $nota The note content
 *
 * @return string HTML for the note icon or empty string
 */

?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Tareas</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de Tareas" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<!-- jQuery UI CSS for autocomplete -->
	<link rel="stylesheet" href="<?php echo RUTA_ADM_ASSETS ?>plugins/jquery-ui-dist/jquery-ui.min.css">
	<?php #endregion HEAD ?>
	
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES ?>css/ltareas.css">
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Tareas</h4>
				<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
					<p class="mb-0 text-muted">Tareas del proyecto: <strong><?php echo htmlspecialchars($nombre_proyecto_filtro); ?></strong></p>
				<?php else: ?>
					<p class="mb-0 text-muted">Selecciona un proyecto para ver sus tareas</p>
				<?php endif; ?>
			</div>
			<div class="ms-auto">
				<?php if ($filtro_proyecto_id): ?>
					<a href="#" class="btn btn-outline-primary me-2" onclick="setProjectSessionAndNavigate(<?php echo $filtro_proyecto_id; ?>); return false;"><i class="fa fa-book fa-fw me-1"></i> Ver Historias</a>
				<?php else: ?>
					<a href="listado-historias" class="btn btn-outline-primary me-2"><i class="fa fa-book fa-fw me-1"></i> Ver Historias</a>
				<?php endif; ?>
				<a href="lproyectos" class="btn btn-outline-primary"><i class="fa fa-folder fa-fw me-1"></i> Ver Proyectos</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region ACTIVE AGENTS PANEL ?>
		<?php if (!empty($agentes_activos)): ?>
			<div class="panel panel-inverse mt-3 no-border-radious" id="agentes-panel">
				<div class="panel-heading no-border-radious d-flex align-items-center cursor-pointer" data-toggle="panel-collapse">
					<h4 class="panel-title mb-0">
						<i class="fa fa-robot me-2"></i>Agentes Activos
					</h4>
					<div class="panel-heading-btn ms-auto">
						<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
					</div>
				</div>
				<div class="panel-body" style="display: none;padding:0">
					<div class="table-nowrap" style="overflow: auto">
						<table class="table table-hover table-sm">
							<thead>
							<tr>
								<th>Descripción</th>
								<th class="text-end">Balance</th>
							</tr>
							</thead>
							<tbody>
							<?php foreach ($agentes_activos as $agente): ?>
								<tr>
									<td><?php echo htmlspecialchars($agente->getDescripcion()); ?></td>
									<td class="text-end"><?php echo number_format($agente->getBalance() ?? 0, 2); ?></td>
								</tr>
							<?php endforeach; ?>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		<?php endif; ?>
		<?php #endregion ACTIVE AGENTS PANEL ?>
		
		<?php #region region FILTER BUTTONS ?>
		<div class="mb-3 d-flex justify-content-between align-items-center">
			<div class="btn-group">
				<?php if (isset($filtro_proyecto_id) && $filtro_proyecto_id): ?>
					<!-- Project-specific filter buttons (maintain project context) -->
					<a href="ltareas" class="btn btn-sm <?php echo !$filtro_estado ? 'btn-primary' : 'btn-default'; ?>">
						Todas
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_PENDIENTE; ?>"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_PENDIENTE ? 'btn-primary' : 'btn-default'; ?>">
						Pendientes
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_EN_PROGRESO; ?>"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_EN_PROGRESO ? 'btn-primary' : 'btn-default'; ?>">
						En Progreso
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_TERMINADO; ?>"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_TERMINADO ? 'btn-primary' : 'btn-default'; ?>">
						Terminadas
					</a>
					<!-- Button to clear project filter and see all tasks -->
					<a href="ltareas?clear_project=1" class="btn btn-sm btn-outline-secondary" title="Ver todas las tareas (sin filtro de proyecto)">
						<i class="fa fa-times"></i> Quitar Filtro
					</a>
				<?php else: ?>
					<!-- Global filter buttons (no project context) -->
					<a href="ltareas?clear_project=1" class="btn btn-sm <?php echo !$filtro_estado ? 'btn-primary' : 'btn-default'; ?>">
						Todas
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_PENDIENTE; ?>&clear_project=1"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_PENDIENTE ? 'btn-primary' : 'btn-default'; ?>">
						Pendientes
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_EN_PROGRESO; ?>&clear_project=1"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_EN_PROGRESO ? 'btn-primary' : 'btn-default'; ?>">
						En Progreso
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_TERMINADO; ?>&clear_project=1"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_TERMINADO ? 'btn-primary' : 'btn-default'; ?>">
						Terminadas
					</a>
				<?php endif; ?>
			</div>
			<div>
				<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro && isset($filtro_proyecto_id) && $filtro_proyecto_id): ?>
					<a href="itarea?proyecto_id=<?php echo $filtro_proyecto_id; ?>" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear tarea</a>
				<?php endif; ?>
			</div>
		</div>
		<?php #endregion FILTER BUTTONS ?>
		
		<?php #region region SPRINT PANEL ?>
		<?php if (isset($filtro_proyecto_id) && $filtro_proyecto_id): ?>
			<div class="panel panel-inverse mt-3 no-border-radious" id="sprint-panel">
				<?php if (isset($active_sprint) && $active_sprint): ?>
					<!-- Active Sprint Display -->
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">
							<i class="fa fa-rocket me-2"></i>Sprint Activo: <?php echo htmlspecialchars($active_sprint->getDescripcion()); ?>
							<div class="float-end">
								<button type="button" class="btn btn-xs btn-primary me-1" id="btn-edit-sprint-descripcion"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Editar descripción">
									<i class="fa fa-edit"></i> Descripción
								</button>
								<button type="button" class="btn btn-xs btn-info me-1" id="btn-edit-sprint-cambios-bd"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Editar cambios BD">
									<i class="fa fa-database"></i> BD
								</button>
								<button type="button" class="btn btn-xs btn-warning me-1" id="btn-edit-sprint-cambios-resources"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Editar cambios Resources">
									<i class="fa fa-folder"></i> Resources
								</button>
								<button type="button" class="btn btn-xs btn-danger" id="btn-finalize-sprint"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Finalizar sprint">
									<i class="fa fa-flag-checkered"></i> Finalizar
								</button>
							</div>
						</h4>
					</div>
					<!-- Sprint Tasks Table -->
					<div class="table-nowrap" style="overflow: auto">
						<table class="table table-hover table-sm">
							<thead>
							<tr>
								<th class="text-center" style="width: 190px;">Acciones</th>
								<th class="text-center" style="width: 30px;">#</th>
								<th>Módulo</th>
								<th>Descripción</th>
								<th class="text-center">Estado</th>
								<th class="text-center">Fecha Terminación</th>
							</tr>
							</thead>
							<tbody class="fs-12px" id="sprint-table-body">
							<?php if (!empty($sprint_tasks_organized)): ?>
								<?php foreach ($sprint_tasks_organized as $item): ?>
									<?php
									$tarea_padre  = $item['tarea'];
									$tareas_hijas = $item['hijas'];
									$tiene_hijas  = !empty($tareas_hijas);
									?>
									
									<!-- Sprint Tarea Padre -->
									<tr data-tarea-id="<?php echo $tarea_padre->getId(); ?>" class="tarea-padre">
										<td class="text-center">
											<?php
											$tarea = $tarea_padre; // Set for the include
											include __ROOT__ . '/views/admin/ltareas_acciones.php';
											?>
										</td>
										<td class="text-center"><?php echo htmlspecialchars((string)$tarea_padre->getId()); ?></td>
										<td><?php echo $tarea_padre->getNombreProyectoModulo() ? htmlspecialchars($tarea_padre->getNombreProyectoModulo()) : 'N/A'; ?></td>
										<td>
											<?php if ($tiene_hijas): ?>
												<button type="button" class="btn-toggle-hijas" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
													<i class="fa fa-chevron-down"></i>
												</button>
											<?php endif; ?>
											<span class="ver-nota" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#verNotaModal" data-descripcion="<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?>" data-modulo="<?php echo htmlspecialchars($tarea_padre->getNombreProyectoModulo() ?? 'N/A'); ?>"
											      data-nota="<?php echo htmlspecialchars($tarea_padre->getNota() ?? 'N/A'); ?>">
												<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?><?php echo renderNoteIcon($tarea_padre->getNota()); ?>
											</span>
											<?php if ($tiene_hijas): ?>
												<span class="badge badge-hijo-count"><?php echo count($tareas_hijas); ?> subtarea<?php echo count($tareas_hijas) > 1 ? 's' : ''; ?></span>
											<?php endif; ?>
										</td>
										<td class="text-center"><span class="badge <?php echo $tarea_padre->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_padre->getNombreTareaEstado()); ?></span></td>
										<td class="text-center"><?php echo format_dateyyymmdd($tarea_padre->getFechaTerminacion()); ?></td>
									</tr>
									
									<!-- Sprint Tareas Hijas -->
									<?php if ($tiene_hijas): ?>
										<?php foreach ($tareas_hijas as $tarea_hija): ?>
											<tr data-tarea-id="<?php echo $tarea_hija->getId(); ?>" class="tarea-hija tarea-hijas-container" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
												<td class="text-center">
													<?php
													$tarea = $tarea_hija; // Set for the include
													include __ROOT__ . '/views/admin/ltareas_acciones.php';
													?>
												</td>
												<td class="text-center"><?php echo htmlspecialchars((string)$tarea_hija->getId()); ?></td>
												<td><?php echo $tarea_hija->getNombreProyectoModulo() ? htmlspecialchars($tarea_hija->getNombreProyectoModulo()) : 'N/A'; ?></td>
												<td>
													<div class="descripcion-hija">
														<span class="ver-nota" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#verNotaModal" data-descripcion="<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?>" data-modulo="<?php echo htmlspecialchars($tarea_hija->getNombreProyectoModulo() ?? 'N/A'); ?>"
														      data-nota="<?php echo htmlspecialchars($tarea_hija->getNota() ?? 'N/A'); ?>">
															<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?><?php echo renderNoteIcon($tarea_hija->getNota()); ?>
														</span>
													</div>
												</td>
												<td class="text-center"><span class="badge <?php echo $tarea_hija->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_hija->getNombreTareaEstado()); ?></span></td>
												<td class="text-center"><?php echo format_dateyyymmdd($tarea_hija->getFechaTerminacion()); ?></td>
											</tr>
										<?php endforeach; ?>
									<?php endif; ?>
								<?php endforeach; ?>
							<?php else: ?>
								<tr>
									<td colspan="6" class="text-center py-4">
										<i class="fa fa-rocket fa-2x text-muted mb-2"></i>
										<p class="text-muted mb-0">No hay tareas asociadas al sprint activo</p>
									</td>
								</tr>
							<?php endif; ?>
							</tbody>
						</table>
					</div>
				<?php else: ?>
					<!-- No Active Sprint - Show Creation Interface -->
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">
							<i class="fa fa-rocket me-2"></i>Sprint
						</h4>
					</div>
					<div class="panel-body text-center py-5">
						<i class="fa fa-plus-circle fa-3x text-muted mb-3"></i>
						<h5 class="text-muted mb-3">No hay sprint activo</h5>
						<p class="text-muted mb-4">Crea un nuevo sprint para organizar las tareas del proyecto</p>
						<button type="button" class="btn btn-success" id="btn-create-sprint">
							<i class="fa fa-plus me-1"></i> Crear Sprint
						</button>
					</div>
				<?php endif; ?>
			</div>
		<?php endif; ?>
		<?php #endregion SPRINT PANEL ?>
		
		<?php #region region PANEL TAREAS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Tareas
					<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
						- <?php echo htmlspecialchars($nombre_proyecto_filtro); ?>
					<?php endif; ?>
					<?php if ($filtro_estado === Tarea::ESTADO_PENDIENTE): ?>
						<?php echo (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro) ? ' (Pendientes)' : 'Pendientes'; ?>
					<?php elseif ($filtro_estado === Tarea::ESTADO_EN_PROGRESO): ?>
						<?php echo (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro) ? ' (En Progreso)' : 'En Progreso'; ?>
					<?php elseif ($filtro_estado === Tarea::ESTADO_TERMINADO): ?>
						<?php echo (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro) ? ' (Terminadas)' : 'Terminadas'; ?>
					<?php endif; ?>
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="table-nowrap" style="overflow: auto">
				<?php #region region TABLE TAREAS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="text-center" style="width: 190px;">Acciones</th>
						<th class="text-center" style="width: 30px;">#</th>
						<th>Módulo</th>
						<th>Descripción</th>
						<th class="text-center">Estado</th>
						<th class="text-center">Fecha Terminación</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="tarea-table-body">
					<?php if (!empty($tareas_organizadas)): ?>
						<?php foreach ($tareas_organizadas as $item): ?>
							<?php
							$tarea_padre  = $item['tarea'];
							$tareas_hijas = $item['hijas'];
							$tiene_hijas  = !empty($tareas_hijas);
							?>
							
							<!-- Tarea Padre -->
							<tr data-tarea-id="<?php echo $tarea_padre->getId(); ?>" class="tarea-padre">
								<td class="text-center">
									<?php
									$tarea = $tarea_padre; // Set for the include
									include __ROOT__ . '/views/admin/ltareas_acciones.php';
									?>
								</td>
								<td class="text-center"><?php echo htmlspecialchars((string)$tarea_padre->getId()); ?></td>
								<td><?php echo $tarea_padre->getNombreProyectoModulo() ? htmlspecialchars($tarea_padre->getNombreProyectoModulo()) : 'N/A'; ?></td>
								<td>
									<?php if ($tiene_hijas): ?>
										<button type="button" class="btn-toggle-hijas" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
											<i class="fa fa-chevron-down"></i>
										</button>
									<?php endif; ?>
									<span class="ver-nota" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#verNotaModal" data-descripcion="<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?>" data-modulo="<?php echo htmlspecialchars($tarea_padre->getNombreProyectoModulo() ?? 'N/A'); ?>"
									      data-nota="<?php echo htmlspecialchars($tarea_padre->getNota() ?? 'N/A'); ?>">
										<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?><?php echo renderNoteIcon($tarea_padre->getNota()); ?>
									</span>
									<?php if ($tiene_hijas): ?>
										<span class="badge badge-hijo-count"><?php echo count($tareas_hijas); ?> subtarea<?php echo count($tareas_hijas) > 1 ? 's' : ''; ?></span>
									<?php endif; ?>
								</td>
								<td class="text-center"><span class="badge <?php echo $tarea_padre->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_padre->getNombreTareaEstado()); ?></span></td>
								<td class="text-center"><?php echo format_dateyyymmdd($tarea_padre->getFechaTerminacion()); ?></td>
							</tr>
							
							<!-- Tareas Hijas -->
							<?php if ($tiene_hijas): ?>
								<?php foreach ($tareas_hijas as $tarea_hija): ?>
									<tr data-tarea-id="<?php echo $tarea_hija->getId(); ?>" class="tarea-hija tarea-hijas-container" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
										<td class="text-center">
											<?php
											$tarea = $tarea_hija; // Set for the include
											include __ROOT__ . '/views/admin/ltareas_acciones.php';
											?>
										</td>
										<td class="text-center"><?php echo htmlspecialchars((string)$tarea_hija->getId()); ?></td>
										<td><?php echo $tarea_hija->getNombreProyectoModulo() ? htmlspecialchars($tarea_hija->getNombreProyectoModulo()) : 'N/A'; ?></td>
										<td>
											<div class="descripcion-hija">
												<span class="ver-nota" style="cursor: pointer;" data-bs-toggle="modal" data-bs-target="#verNotaModal" data-descripcion="<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?>" data-modulo="<?php echo htmlspecialchars($tarea_hija->getNombreProyectoModulo() ?? 'N/A'); ?>"
												      data-nota="<?php echo htmlspecialchars($tarea_hija->getNota() ?? 'N/A'); ?>">
													<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?><?php echo renderNoteIcon($tarea_hija->getNota()); ?>
												</span>
											</div>
										</td>
										<td class="text-center"><span class="badge <?php echo $tarea_hija->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_hija->getNombreTareaEstado()); ?></span></td>
										<td class="text-center"><?php echo format_dateyyymmdd($tarea_hija->getFechaTerminacion()); ?></td>
									</tr>
								<?php endforeach; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php else: ?>
						<tr>
							<td colspan="6" class="text-center py-5">
								<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
									<i class="fa fa-tasks fa-3x text-muted mb-3"></i>
									<p class="text-muted mb-0">No hay tareas para el proyecto "<?php echo htmlspecialchars($nombre_proyecto_filtro); ?>".</p>
								<?php else: ?>
									<i class="fa fa-folder-open fa-3x text-muted mb-3"></i>
									<p class="text-muted mb-2"><strong>Selecciona un proyecto para ver sus tareas</strong></p>
									<p class="text-muted mb-0">Ve a la <a href="lproyectos" class="text-decoration-none">lista de proyectos</a> y haz clic en el botón "Ver Tareas" del proyecto que deseas gestionar.</p>
								<?php endif; ?>
							</td>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
				<?php #endregion TABLE TAREAS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL TAREAS ?>
	</div>
	<!-- END #content -->
	
	<!-- Toast notification container -->
	<div class="toast-container" id="toast-container"></div>
	
	<?php #region Ver Nota Modal ?>
	<div class="modal fade" id="verNotaModal" tabindex="-1" aria-labelledby="verNotaModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="verNotaModalLabel">Detalles de la Tarea</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="row mb-3">
						<div class="col-md-8">
							<div class="card bg-light border-start border-primary border-4 shadow-sm h-100">
								<div class="card-body">
									<h6 class="card-subtitle mb-2 text-muted"><i class="fa fa-tasks me-2"></i>Tarea</h6>
									<p class="card-text fs-5" id="modal-ver-nota-descripcion"></p>
								</div>
							</div>
						</div>
						<div class="col-md-4">
							<div class="card bg-light border-start border-info border-4 shadow-sm h-100">
								<div class="card-body">
									<h6 class="card-subtitle mb-2 text-muted"><i class="fa fa-puzzle-piece me-2"></i>Módulo</h6>
									<p class="card-text fs-5" id="modal-ver-nota-modulo"></p>
								</div>
							</div>
						</div>
					</div>
					<div class="mb-3">
						<label for="modal-ver-nota-text" class="form-label"><strong>Nota:</strong></label>
						<textarea class="form-control bg-light text-white-500" id="modal-ver-nota-text" rows="10" readonly></textarea>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	<?php #endregion Ver Nota Modal ?>
	
	<?php #region region Gestionar Agentes Modal ?>
	<div class="modal fade" id="gestionarAgentesModal" tabindex="-1" aria-labelledby="gestionarAgentesModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="gestionarAgentesModalLabel">Gestionar Agentes de la Tarea</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="agentes-loading" class="text-center" style="display: none;">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Cargando...</span>
						</div>
						<p class="mt-2">Cargando agentes asociados...</p>
					</div>
					<div id="agentes-error" class="alert alert-danger" style="display: none;"></div>
					<div id="agentes-content">
						<div class="row mb-4">
							<div class="col-md-8">
								<div class="card bg-light border-start border-primary border-4 shadow-sm h-100">
									<div class="card-body d-flex flex-column">
										<h6 class="card-subtitle mb-1 text-muted d-flex align-items-center">
											<i class="fa fa-tasks me-2"></i>Tarea
										</h6>
										<p class="card-text fs-5 mb-0 mt-auto" id="modal-tarea-descripcion"></p>
									</div>
								</div>
							</div>
							<div class="col-md-4">
								<div class="card bg-light border-start border-info border-4 shadow-sm h-100">
									<div class="card-body d-flex flex-column">
										<h6 class="card-subtitle mb-1 text-muted d-flex align-items-center">
											<i class="fa fa-puzzle-piece me-2"></i>Módulo
										</h6>
										<p class="card-text fs-5 mb-0 mt-auto" id="modal-tarea-modulo"></p>
									</div>
								</div>
							</div>
						</div>
						
						<!-- Add New Agent Form -->
						<div class="panel panel-inverse mb-3">
							<div class="panel-heading">
								<h4 class="panel-title">Asignar Nuevo Agente</h4>
							</div>
							<div class="panel-body">
								<form id="add-agente-form">
									<div class="row">
										<div class="col-md-3">
											<label for="id_agente" class="form-label">Agente <span class="text-danger">*</span></label>
											<select class="form-select" id="id_agente" name="id_agente" required>
												<option value="">-- Seleccione un agente --</option>
												<!-- Options will be populated via AJAX -->
											</select>
										</div>
										<div class="col-md-3">
											<label for="costo_usd" class="form-label">Costo USD</label>
											<input type="number" class="form-control" id="costo_usd" name="costo_usd"
											       step="0.01" min="0" placeholder="0.00">
										</div>
										<div class="col-md-3">
											<label for="n_mensajes" class="form-label">N° Mensajes</label>
											<input type="number" class="form-control" id="n_mensajes" name="n_mensajes"
											       min="0" placeholder="0">
										</div>
										<div class="col-md-3">
											<label class="form-label balance-label">Balance</label>
											<div class="balance-field">
												<div id="agente_balance_display" style="display: none;">
													0.00
												</div>
												<div id="agente_balance_placeholder">
													Seleccione un agente
												</div>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-md-12">
											<div class="mb-3">
												<label class="form-label">&nbsp;</label>
												<button type="submit" class="btn btn-success d-block w-100">
													<i class="fa fa-plus"></i> Asignar
												</button>
											</div>
										</div>
									</div>
								</form>
								<div id="add-agente-feedback"></div>
							</div>
						</div>
						
						<!-- Existing Agents Table -->
						<div class="panel panel-inverse">
							<div class="panel-heading">
								<h4 class="panel-title">Agentes Asignados</h4>
							</div>
							<div class="panel-body p-0">
								<div class="table-responsive">
									<table class="table table-hover mb-0">
										<thead>
										<tr class="text-nowrap">
											<th class="text-center w-80px">Acciones</th>
											<th>Agente</th>
											<th class="text-end">Costo USD</th>
											<th class="text-center">N° Mensajes</th>
											<th class="text-end">Balance</th>
										</tr>
										</thead>
										<tbody id="agentes-table-body">
										<!-- Content will be populated via AJAX -->
										</tbody>
									</table>
								</div>
								<div id="agentes-empty" class="text-center text-muted p-4" style="display: none;">
									<i class="fa fa-info-circle fa-2x mb-2"></i>
									<p>La tarea no tiene agentes asociados</p>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	<?php #endregion Gestionar Agentes Modal ?>
	
	<!-- Edit TareaAgente Modal -->
	<div class="modal fade" id="editAgenteModal" tabindex="-1" aria-labelledby="editAgenteModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="edit-agente-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editAgenteModalLabel">Editar Asignación de Agente</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="edit-tarea-agente-id" name="id">
						<div class="mb-3">
							<label for="edit_id_agente" class="form-label">Agente <span class="text-danger">*</span></label>
							<select class="form-select" id="edit_id_agente" name="id_agente" required>
								<option value="">-- Seleccione un agente --</option>
								<!-- Options will be populated via AJAX -->
							</select>
						</div>
						<div class="mb-3">
							<label for="edit_costo_usd" class="form-label">Costo USD</label>
							<input type="number" class="form-control" id="edit_costo_usd" name="costo_usd"
							       step="0.01" min="0" placeholder="0.00">
						</div>
						<div class="mb-3">
							<label for="edit_n_mensajes" class="form-label">N° Mensajes</label>
							<input type="number" class="form-control" id="edit_n_mensajes" name="n_mensajes"
							       min="0" placeholder="0">
						</div>
						<div id="edit-agente-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Actualizar</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- Edit Tarea Modal -->
	<div class="modal fade" id="editarTareaModal" tabindex="-1" aria-labelledby="editarTareaModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<form id="editar-tarea-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editarTareaModalLabel">Editar Tarea</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="editar-tarea-id" name="id">
						<input type="hidden" id="editar-tarea-id-proyecto" name="id_proyecto">
						<input type="hidden" id="editar-modulo-hidden" name="id_proyecto_modulo">
						
						<div class="mb-3">
							<label for="editar-tarea-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="editar-tarea-descripcion" name="descripcion" required>
							<div class="invalid-feedback" id="editar-descripcion-error" style="display: none;">La descripción es requerida.</div>
						</div>
						
						<div class="mb-3">
							<label for="editar-modulo-autocomplete" class="form-label">Módulo</label>
							<div class="input-group">
								<div class="position-relative flex-grow-1">
									<input type="text"
									       class="form-control"
									       id="editar-modulo-autocomplete"
									       placeholder="Escriba para buscar un módulo..."
									       autocomplete="off">
									<div id="editar-modulo-loading" class="position-absolute top-50 end-0 translate-middle-y me-2" style="display: none;">
										<div class="spinner-border spinner-border-sm text-primary" role="status">
											<span class="visually-hidden">Cargando...</span>
										</div>
									</div>
								</div>
								<button type="button" class="btn btn-outline-success" id="editar-btn-nuevo-modulo" title="Crear nuevo módulo">
									<i class="fa fa-plus"></i>
								</button>
							</div>
							<div id="editar-modulo-error" class="text-danger small" style="display: none;"></div>
						</div>
						
						<div class="mb-3">
							<label for="editar-historia-select" class="form-label">Historia <span class="text-danger">*</span></label>
							<select class="form-select" id="editar-historia-select" name="id_historia" required>
								<option value="">-- Seleccione una historia --</option>
								<!-- Options will be populated when modal opens -->
							</select>
							<div id="editar-historia-error" class="text-danger small" style="display: none;"></div>
						</div>
						
						<div class="mb-3">
							<label for="editar-tarea-nota" class="form-label">Nota</label>
							<textarea class="form-control" id="editar-tarea-nota" name="nota" rows="10" placeholder="Ingrese una nota para esta tarea..."></textarea>
						</div>
						
						<div class="mb-3" id="editar-sprint-association-container" style="display: none;">
							<div class="form-check">
								<input class="form-check-input" type="checkbox" id="editar-sprint-association" name="sprint_association">
								<label class="form-check-label" for="editar-sprint-association">
									<i class="fa fa-rocket me-1"></i>
									<span id="editar-sprint-association-text">Asociar tarea al sprint actual</span>
								</label>
							</div>
							<div class="form-text" id="editar-sprint-association-help">
								<span id="editar-sprint-current-name"></span>
							</div>
						</div>
						
						<div id="editar-tarea-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Actualizar</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- Create Modulo Modal for Edit Task -->
	<div class="modal fade" id="editarCreateModuloModal" tabindex="-1" aria-labelledby="editarCreateModuloModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="editar-create-modulo-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editarCreateModuloModalLabel">Crear Nuevo Módulo</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="editar-nuevo-modulo-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="editar-nuevo-modulo-descripcion" name="descripcion" required>
						</div>
						<div id="editar-nuevo-modulo-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Crear Módulo</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- Sprint Creation Modal -->
	<div class="modal fade" id="createSprintModal" tabindex="-1" aria-labelledby="createSprintModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="create-sprint-form">
					<div class="modal-header">
						<h5 class="modal-title" id="createSprintModalLabel">Crear Nuevo Sprint</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="sprint-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="sprint-descripcion" name="descripcion" required>
							<div class="form-text">Se generará automáticamente como: ID{Sprint_ID} {Tu_Descripción}</div>
						</div>
						<div id="create-sprint-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-success">Crear Sprint</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- Sprint Field Edit Modal -->
	<div class="modal fade" id="editSprintFieldModal" tabindex="-1" aria-labelledby="editSprintFieldModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<form id="edit-sprint-field-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editSprintFieldModalLabel">Editar Campo del Sprint</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="edit-sprint-id" name="sprint_id">
						<input type="hidden" id="edit-sprint-field" name="field">
						
						<div class="mb-3" id="descripcion-field" style="display: none;">
							<label for="edit-sprint-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="edit-sprint-descripcion" name="descripcion">
						</div>
						
						<div class="mb-3" id="cambios-bd-field" style="display: none;">
							<label for="edit-sprint-cambios-bd" class="form-label">Cambios en Base de Datos</label>
							<textarea class="form-control" id="edit-sprint-cambios-bd" name="cambios_bd" rows="30"
							          placeholder="Describe los cambios realizados en la base de datos..."></textarea>
						</div>
						
						<div class="mb-3" id="cambios-resources-field" style="display: none;">
							<label for="edit-sprint-cambios-resources" class="form-label">Cambios en Resources</label>
							<textarea class="form-control" id="edit-sprint-cambios-resources" name="cambios_resources" rows="30"
							          placeholder="Describe los cambios realizados en archivos de recursos..."></textarea>
						</div>
						
						<div id="edit-sprint-field-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Actualizar</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<!-- jQuery UI for autocomplete functionality -->
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/jquery-ui-dist/jquery-ui.min.js"></script>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    // Configuration object to pass PHP data to JavaScript
    window.ltareasConfig = {
        filtroEstado    : <?php echo json_encode($filtro_estado); ?>,
        filtroProyectoId: <?php echo json_encode($filtro_proyecto_id); ?>,
        tareaEstados    : {
            PENDIENTE  : <?php echo Tarea::ESTADO_PENDIENTE; ?>,
            EN_PROGRESO: <?php echo Tarea::ESTADO_EN_PROGRESO; ?>,
            TERMINADO  : <?php echo Tarea::ESTADO_TERMINADO; ?>
        }
    };
</script>

<script src="<?php echo RUTA_RESOURCES ?>js/ltareas.js"></script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
